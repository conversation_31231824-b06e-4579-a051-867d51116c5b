<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据隐患辨一辨</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
            background-attachment: fixed;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #2b50c7;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            font-size: 2.5em;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        .game-area {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .category {
            width: 45%;
            min-height: 300px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            transition: transform 0.3s, box-shadow 0.3s;
            position: relative;
            overflow: hidden;
        }
        .category:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
            background: rgba(255, 255, 255, 0.25);
            border: 2px solid rgba(255, 255, 255, 0.4);
        }
        .category h2 {
            text-align: center;
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
            font-size: 1.8em;
        }
        .category::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            animation: shine 2s infinite;
        }
        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .danger {
            border-color: #ff6b6b;
        }
        .danger h2 {
            color: #ff3333;
        }
        .safe {
            border-color: #51cf66;
        }
        .safe h2 {
            color: #2fb543;
        }
        .items-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-top: 30px;
            justify-content: center;
            perspective: 1000px;
        }
        .item {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 12px 18px;
            cursor: move;
            user-select: none;
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
            transition: all 0.3s;
            font-size: 1.1em;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        .item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.8), transparent);
            transform: translateX(-100%);
            transition: 0.6s;
            z-index: -1;
        }
        .item:hover {
            transform: translateY(-5px) rotate(2deg);
            box-shadow: 0 8px 24px rgba(31, 38, 135, 0.5);
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
        }
        .item:hover::before {
            transform: translateX(100%);
        }
        .item.dragging {
            opacity: 0.7;
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        .dropped-item {
            margin: 12px 0;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            animation: dropIn 0.5s forwards;
            opacity: 0;
            transform: translateY(-10px);
        }
        @keyframes dropIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .btn-container {
            text-align: center;
            margin-top: 30px;
        }
        .reset-btn {
            background-color: #4dabf7;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(77, 171, 247, 0.4);
            position: relative;
            overflow: hidden;
        }
        .reset-btn:hover {
            background-color: #339af0;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(77, 171, 247, 0.6);
        }
        .reset-btn:active {
            transform: translateY(0);
        }
        .reset-btn::after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        .reset-btn:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        .feedback {
            margin-top: 10px;
            margin-bottom: 25px;
            font-size: 1.5em;
            min-height: 30px;
            color: #ffffff;
            text-align: center;
            font-weight: bold;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(31, 38, 135, 0.3);
        }
        .item-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }
        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background-color: #f00;
            animation: confetti-fall 3s ease-in-out infinite;
            z-index: 9999;
        }
        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(720deg);
                opacity: 0;
            }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
        }
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        .home-btn {
            background-color: #51cf66;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(81, 207, 102, 0.4);
            position: relative;
            overflow: hidden;
            margin-left: 15px;
            display: none;
        }
        .home-btn:hover {
            background-color: #40c057;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.6);
        }
        .security-shield {
            position: fixed;
            width: 100px;
            height: 100px;
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232b50c7"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            opacity: 0;
            z-index: 100;
            filter: drop-shadow(0 0 10px rgba(43, 80, 199, 0.7));
            animation: float 3s ease-in-out infinite;
            display: none;
        }
        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0px) rotate(0deg); }
        }
        .show-shield {
            display: block;
            opacity: 1;
            animation: fadeIn 1s ease-in-out forwards, float 3s ease-in-out infinite;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* 添加弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            max-width: 500px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            animation: modalIn 0.5s ease-out forwards;
            transform: translateY(-50px);
            opacity: 0;
        }
        
        @keyframes modalIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .modal-title {
            color: #2b50c7;
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .modal-text {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
        }
        
        .shield-container {
            margin: 20px auto;
            width: 100px;
            height: 100px;
            position: relative;
        }
        
        .modal-btn {
            background-color: #4dabf7;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .modal-btn:hover {
            background-color: #339af0;
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据隐患辨一辨</h1>
        
        <div class="feedback" id="feedback">拖动卡片到正确的分类中吧！</div>
        
        <div class="game-area">
            <div class="category danger" id="danger-category">
                <h2>隐患陷阱</h2>
                <div id="danger-items"></div>
            </div>
            
            <div class="category safe" id="safe-category">
                <h2>安全行为</h2>
                <div id="safe-items"></div>
            </div>
        </div>
        
        <div class="items-container" id="items">
            <div class="item" draggable="true" data-item="stranger-friend" data-type="danger">👤 陌生人添加你好友</div>
            <div class="item" draggable="true" data-item="unknown-email" data-type="danger">📧 陌生的邮件链接</div>
            <div class="item" draggable="true" data-item="unknown-qrcode" data-type="danger">📱 来历不明的二维码</div>
            <div class="item" draggable="true" data-item="cheap-gift" data-type="danger">🎁 花1分钱领取礼品</div>
            <div class="item" draggable="true" data-item="express-box" data-type="danger">📦 随意丢弃快递盒</div>
            <div class="item" draggable="true" data-item="cheap-skin" data-type="danger">👾 低价领取游戏皮肤</div>
            <div class="item" draggable="true" data-item="antivirus" data-type="safe">🛡️ 安装杀毒软件</div>
            <div class="item" draggable="true" data-item="password" data-type="safe">🔒 电脑设置登录密码</div>
        </div>
        
        <div class="btn-container">
            <button class="reset-btn" id="reset-btn">重新开始</button>
            <button class="home-btn" id="home-btn">回到主界面</button>
        </div>
    </div>
    
    <!-- 添加弹窗 -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="modal-title">恭喜你闯关成功！</div>
            <div class="modal-text">你已成功识别所有数据隐患，获得一个数据防护盾！</div>
            <div class="shield-container">
                <div class="security-shield show-shield" id="security-shield"></div>
            </div>
            <button class="modal-btn" id="modal-close-btn">继续</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取元素容器
            const itemsContainer = document.getElementById('items');
            // 获取所有可拖动元素的HTML内容并转换为数组
            const itemElements = Array.from(itemsContainer.children);
            
            // 随机排序可拖动元素
            function shuffleItems() {
                // 创建一个临时数组来存储元素
                const tempArray = Array.from(itemElements);
                
                // Fisher-Yates 洗牌算法
                for (let i = tempArray.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [tempArray[i], tempArray[j]] = [tempArray[j], tempArray[i]];
                }
                
                // 清空容器
                itemsContainer.innerHTML = '';
                
                // 将洗牌后的元素重新添加到容器
                tempArray.forEach(item => {
                    // 创建克隆节点以避免引用问题
                    const clonedItem = item.cloneNode(true);
                    itemsContainer.appendChild(clonedItem);
                });
                
                console.log('元素已随机排序');
            }
            
            // 保存分类结果到localStorage
            function saveClassificationResult() {
                const items = document.querySelectorAll('.item');
                const savedResults = {};
                
                items.forEach(item => {
                    const itemId = item.getAttribute('data-item');
                    const itemType = item.getAttribute('data-type');
                    const isPlaced = item.style.display === 'none';
                    
                    savedResults[itemId] = {
                        type: itemType,
                        placed: isPlaced
                    };
                });
                
                // 获取已放置的项
                const dangerItems = Array.from(document.querySelectorAll('#danger-items .dropped-item')).map(item => {
                    return {
                        id: item.getAttribute('data-item'),
                        text: item.textContent
                    };
                });
                
                const safeItems = Array.from(document.querySelectorAll('#safe-items .dropped-item')).map(item => {
                    return {
                        id: item.getAttribute('data-item'),
                        text: item.textContent
                    };
                });
                
                // 保存分类结果和已放置的项
                localStorage.setItem('classification_results', JSON.stringify(savedResults));
                localStorage.setItem('danger_items', JSON.stringify(dangerItems));
                localStorage.setItem('safe_items', JSON.stringify(safeItems));
                
                // 检查是否所有项目都已分类
                const allPlaced = Object.values(savedResults).every(result => result.placed);
                if (allPlaced) {
                    localStorage.setItem('level1_completed', 'true');
                }
            }
            
            // 从localStorage加载保存的分类结果
            function loadSavedClassification() {
                const savedResults = localStorage.getItem('classification_results');
                const dangerItemsData = localStorage.getItem('danger_items');
                const safeItemsData = localStorage.getItem('safe_items');
                
                if (!savedResults || !dangerItemsData || !safeItemsData) {
                    return false; // 没有保存的分类结果
                }
                
                try {
                    const parsedResults = JSON.parse(savedResults);
                    const dangerItems = JSON.parse(dangerItemsData);
                    const safeItems = JSON.parse(safeItemsData);
                    
                    // 恢复已分类的项
                    const items = document.querySelectorAll('.item');
                    items.forEach(item => {
                        const itemId = item.getAttribute('data-item');
                        if (parsedResults[itemId] && parsedResults[itemId].placed) {
                            item.style.display = 'none';
                        }
                    });
                    
                    // 恢复危险区域的项
                    const dangerContainer = document.getElementById('danger-items');
                    dangerItems.forEach(item => {
                        const newItem = document.createElement('div');
                        newItem.classList.add('dropped-item');
                        newItem.textContent = item.text;
                        newItem.setAttribute('data-item', item.id);
                        newItem.style.borderLeft = '4px solid #51cf66';
                        dangerContainer.appendChild(newItem);
                    });
                    
                    // 恢复安全区域的项
                    const safeContainer = document.getElementById('safe-items');
                    safeItems.forEach(item => {
                        const newItem = document.createElement('div');
                        newItem.classList.add('dropped-item');
                        newItem.textContent = item.text;
                        newItem.setAttribute('data-item', item.id);
                        newItem.style.borderLeft = '4px solid #51cf66';
                        safeContainer.appendChild(newItem);
                    });
                    
                    // 检查是否所有项目都已分类
                    const allPlaced = Object.values(parsedResults).every(result => result.placed);
                    if (allPlaced) {
                        feedbackElement.textContent = '🎉 恭喜你完成了所有分类！';
                        feedbackElement.style.color = '#ffffff';
                        feedbackElement.style.backgroundColor = 'transparent';
                        
                        // 显示回到主界面按钮
                        document.getElementById('home-btn').style.display = 'inline-block';
                    }
                    
                    return true; // 成功恢复保存的分类结果
                } catch (error) {
                    console.error('恢复分类结果时出错:', error);
                    return false; // 恢复失败
                }
            }
            
            // 检查并加载保存的分类结果
            const hasSavedResults = loadSavedClassification();
            
            // 如果没有保存的分类结果，则随机排序元素
            if (!hasSavedResults) {
                // 页面加载时立即随机排序元素
                shuffleItems();
            }
            
            // 获取重新排序后的元素
            const items = document.querySelectorAll('.item');
            const dangerCategory = document.getElementById('danger-category');
            const safeCategory = document.getElementById('safe-category');
            const dangerItems = document.getElementById('danger-items');
            const safeItems = document.getElementById('safe-items');
            const resetBtn = document.getElementById('reset-btn');
            const feedbackElement = document.getElementById('feedback');
            
            // 为每个可拖动项添加事件监听器
            items.forEach(item => {
                item.addEventListener('dragstart', dragStart);
                item.addEventListener('dragend', dragEnd);
            });
            
            // 为放置区域添加事件监听器
            [dangerCategory, safeCategory].forEach(category => {
                category.addEventListener('dragover', dragOver);
                category.addEventListener('dragenter', dragEnter);
                category.addEventListener('dragleave', dragLeave);
                category.addEventListener('drop', drop);
            });
            
            // 拖动开始时
            function dragStart() {
                this.classList.add('dragging');
            }
            
            // 拖动结束时
            function dragEnd() {
                this.classList.remove('dragging');
            }
            
            // 拖动经过目标区域时
            function dragOver(e) {
                e.preventDefault();
            }
            
            // 进入目标区域时
            function dragEnter(e) {
                e.preventDefault();
                this.style.backgroundColor = 'rgba(240, 240, 240, 0.7)';
            }
            
            // 离开目标区域时
            function dragLeave() {
                this.style.backgroundColor = '';
            }
            
            // 创建五彩纸屑效果
            function createConfetti() {
                const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
                
                for (let i = 0; i < 50; i++) {
                    const confetti = document.createElement('div');
                    confetti.classList.add('confetti');
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.width = Math.random() * 10 + 5 + 'px';
                    confetti.style.height = Math.random() * 10 + 5 + 'px';
                    confetti.style.animationDuration = Math.random() * 2 + 2 + 's';
                    document.body.appendChild(confetti);
                    
                    // 动画结束后移除元素
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }
            }
            
            // 放置时
            function drop(e) {
                e.preventDefault();
                this.style.backgroundColor = '';
                
                const draggingItem = document.querySelector('.dragging');
                if (!draggingItem) return;
                
                const itemText = draggingItem.textContent;
                const itemId = draggingItem.getAttribute('data-item');
                const itemType = draggingItem.getAttribute('data-type');
                
                // 判断是否放置正确
                if ((this === dangerCategory && itemType === 'danger') || 
                    (this === safeCategory && itemType === 'safe')) {
                    // 创建新的元素添加到目标区域
                    const newItem = document.createElement('div');
                    newItem.classList.add('dropped-item');
                    newItem.textContent = itemText;
                    newItem.setAttribute('data-item', itemId);
                    newItem.style.borderLeft = '4px solid #51cf66';
                    
                    if (this === dangerCategory) {
                        dangerItems.appendChild(newItem);
                    } else {
                        safeItems.appendChild(newItem);
                    }
                    
                    // 隐藏原始拖动项
                    draggingItem.style.display = 'none';
                    
                    feedbackElement.textContent = '👍 太棒了！分类正确！';
                    feedbackElement.style.color = '#2fb543';
                    feedbackElement.style.backgroundColor = 'transparent';
                    
                    // 保存分类结果
                    saveClassificationResult();
                    
                    // 检查是否所有项目都已分类
                    const visibleItems = document.querySelectorAll('.item[style="display: none;"]');
                    const totalItems = document.querySelectorAll('.item').length;
                    if (visibleItems.length === totalItems) {
                        feedbackElement.textContent = '🎉 恭喜你完成了所有分类！';
                        feedbackElement.style.color = '#ffffff';
                        feedbackElement.style.backgroundColor = 'transparent';
                        createConfetti();
                        
                        // 显示回到主界面按钮
                        document.getElementById('home-btn').style.display = 'inline-block';
                        
                        // 确保保存完整的分类结果
                        saveClassificationResult();
                        
                        // 显示成功弹窗和数据防护盾
                        setTimeout(() => {
                            const modal = document.getElementById('success-modal');
                            modal.style.display = 'flex';
                        }, 1000);
                    }
                } else {
                    // 分类错误，显示提示
                    feedbackElement.textContent = '❌ 再想想，这个分类不对哦！';
                    feedbackElement.style.color = '#ff3333';
                    feedbackElement.style.backgroundColor = 'transparent';
                    
                    // 让元素有一个轻微的震动效果
                    draggingItem.classList.add('shake');
                    setTimeout(() => {
                        draggingItem.classList.remove('shake');
                        draggingItem.classList.remove('dragging');
                    }, 500);
                    
                    // AI助手提供错误原因说明
                    if (window.aiAssistant) {
                        setTimeout(() => {
                            provideErrorExplanation(itemText, itemType, this.id, window.aiAssistant);
                        }, 800);
                    }
                }
            }
            
            // 重置按钮功能
            resetBtn.addEventListener('click', function() {
                // 清空已放置的项
                dangerItems.innerHTML = '';
                safeItems.innerHTML = '';
                
                // 恢复所有原始拖动项
                document.querySelectorAll('.item').forEach(item => {
                    item.style.display = '';
                });
                
                // 重置反馈
                feedbackElement.textContent = '拖动卡片到正确的分类中吧！';
                feedbackElement.style.color = '#ffffff';
                feedbackElement.style.backgroundColor = 'transparent';
                
                // 隐藏回到主界面按钮
                document.getElementById('home-btn').style.display = 'none';
                
                // 清除保存的分类结果
                localStorage.removeItem('classification_results');
                localStorage.removeItem('danger_items');
                localStorage.removeItem('safe_items');
                localStorage.removeItem('level1_completed');
                
                // 重新随机排序元素
                window.location.reload(); // 通过重新加载页面来确保随机排序
            });
            
            // 回到主界面按钮功能
            document.getElementById('home-btn').addEventListener('click', function() {
                // 设置一个标记，表示用户已完成分类游戏
                localStorage.setItem('level1_completed', 'true');
                window.location.href = '../数据安全小卫士闯关乐园.html';
            });
            
            // 弹窗关闭按钮功能
            document.getElementById('modal-close-btn').addEventListener('click', function() {
                document.getElementById('success-modal').style.display = 'none';
                
                // 确保分类结果已保存
                saveClassificationResult();
            });
        });
        
        // 提供错误原因说明的函数
        function provideErrorExplanation(itemText, correctType, droppedZoneId, assistant) {
            let explanation = '';
            let reasoning = '';
            
            // 分析具体的错误情况
            if (itemText.includes('陌生人添加你好友')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "陌生人添加你好友"应该归类为隐患陷阱！';
                    reasoning = '🔍 原因分析：陌生人可能是骗子或恶意用户，添加好友后可能会：\n• 获取你的个人信息和朋友圈内容\n• 发送诈骗信息或恶意链接\n• 冒充你的身份欺骗你的朋友\n💡 正确做法：谨慎添加陌生人，验证身份后再决定。';
                }
            } else if (itemText.includes('陌生的邮件链接')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "陌生的邮件链接"是典型的隐患陷阱！';
                    reasoning = '🔍 原因分析：陌生邮件链接可能是：\n• 钓鱼网站，窃取你的账号密码\n• 恶意软件下载链接\n• 诈骗网站，骗取个人信息或金钱\n💡 正确做法：不要点击来源不明的邮件链接。';
                }
            } else if (itemText.includes('来历不明的二维码')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "来历不明的二维码"存在安全风险！';
                    reasoning = '🔍 原因分析：扫描不明二维码可能导致：\n• 跳转到恶意网站\n• 自动下载病毒软件\n• 泄露手机信息\n• 被恶意扣费\n💡 正确做法：只扫描可信来源的二维码。';
                }
            } else if (itemText.includes('花1分钱领取礼品')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "花1分钱领取礼品"是常见的诱骗手段！';
                    reasoning = '🔍 原因分析：这类活动通常是：\n• 收集个人信息的陷阱\n• 诱导你填写更多敏感信息\n• 可能涉及后续的诈骗行为\n• "天下没有免费的午餐"\n💡 正确做法：对异常便宜的优惠保持警惕。';
                }
            } else if (itemText.includes('随意丢弃快递盒')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "随意丢弃快递盒"会泄露个人信息！';
                    reasoning = '🔍 原因分析：快递盒上通常包含：\n• 你的真实姓名和地址\n• 手机号码\n• 购买习惯信息\n• 被不法分子利用进行诈骗\n💡 正确做法：撕掉或涂黑个人信息后再丢弃。';
                }
            } else if (itemText.includes('低价领取游戏皮肤')) {
                if (droppedZoneId === 'safe-category') {
                    explanation = '❌ "低价领取游戏皮肤"可能是诈骗陷阱！';
                    reasoning = '🔍 原因分析：这类活动可能：\n• 要求你提供游戏账号密码\n• 诱导你下载恶意软件\n• 收集你的个人信息用于其他用途\n• 最终并不会给你任何奖励\n💡 正确做法：通过官方渠道获取游戏道具。';
                }
            } else if (itemText.includes('安装杀毒软件')) {
                if (droppedZoneId === 'danger-category') {
                    explanation = '❌ "安装杀毒软件"是保护电脑的安全行为！';
                    reasoning = '🔍 原因分析：杀毒软件的作用：\n• 检测和清除电脑病毒\n• 防止恶意软件入侵\n• 保护个人数据安全\n• 是电脑安全的基本防护\n💡 正确做法：选择知名的杀毒软件并定期更新。';
                }
            } else if (itemText.includes('电脑设置登录密码')) {
                if (droppedZoneId === 'danger-category') {
                    explanation = '❌ "电脑设置登录密码"是重要的安全措施！';
                    reasoning = '🔍 原因分析：设置登录密码可以：\n• 防止他人随意使用你的电脑\n• 保护电脑中的个人文件和隐私\n• 防止未经授权的数据访问\n• 是基本的数据保护措施\n💡 正确做法：设置复杂密码并定期更换。';
                }
            }
            
            // 发送说明消息
            if (explanation && reasoning) {
                assistant.addMessage(explanation, 'assistant');
                setTimeout(() => {
                    assistant.addMessage(reasoning, 'assistant');
                }, 1500);
            }
        }
        
        // 初始化AI助手
        function initAI() {
            console.log('开始初始化AI助手...');
            
            // 引入AI助手脚本
            const aiScript = document.createElement('script');
            aiScript.src = '../ai-assistant.js';
            aiScript.onload = function() {
                console.log('AI助手脚本加载完成');
                
                const configScript = document.createElement('script');
                configScript.src = '../ai-config.js';
                configScript.onload = function() {
                    console.log('AI配置脚本加载完成');
                    
                    try {
                        // 初始化AI助手
                        const assistant = window.initPageAIAssistant({
                            context: '数据隐患识别游戏'
                        });
                        
                        if (assistant) {
                            console.log('AI助手初始化成功');
                            window.aiAssistant = assistant;
                            
                            // 监听拖拽操作，提供具体指导
                            const dropzones = document.querySelectorAll('.category');
                            dropzones.forEach(zone => {
                                zone.addEventListener('drop', function(e) {
                                    const draggedItem = document.querySelector('.dragging');
                                    if (draggedItem) {
                                        const itemText = draggedItem.textContent;
                                        const itemType = draggedItem.getAttribute('data-type');
                                        const isCorrect = (this.id === 'danger-category' && itemType === 'danger') || 
                                                        (this.id === 'safe-category' && itemType === 'safe');
                                        
                                        setTimeout(() => {
                                            if (isCorrect) {
                                                let tip = '';
                                                if (itemText.includes('陌生人')) {
                                                    tip = '👍 很好！陌生人添加好友确实有风险，可能想获取你的个人信息。';
                                                } else if (itemText.includes('邮件')) {
                                                    tip = '👍 正确！陌生邮件链接可能是钓鱼网站，不要随便点击。';
                                                } else if (itemText.includes('二维码')) {
                                                    tip = '👍 没错！不明来源的二维码可能导向危险网站。';
                                                } else if (itemText.includes('杀毒软件')) {
                                                    tip = '👍 太棒了！杀毒软件是保护电脑安全的重要工具。';
                                                } else if (itemText.includes('密码')) {
                                                    tip = '👍 很好！设置密码可以防止别人随意使用你的电脑。';
                                                }
                                                
                                                if (tip) {
                                                    assistant.addMessage(tip, 'assistant');
                                                }
                                            }
                                        }, 500);
                                    }
                                });
                            });
                            
                            // 监听游戏完成事件
                            const observer = new MutationObserver(function(mutations) {
                                mutations.forEach(function(mutation) {
                                    if (mutation.type === 'childList') {
                                        const modal = document.getElementById('success-modal');
                                        if (modal && modal.style.display === 'flex') {
                                            setTimeout(() => {
                                                assistant.addMessage('🎉 太棒了！你成功识别了所有的数据安全隐患！现在你是数据安全小专家了！', 'assistant');
                                                assistant.showNotification();
                                            }, 1000);
                                        }
                                    }
                                });
                            });
                            
                            observer.observe(document.body, {
                                childList: true,
                                subtree: true,
                                attributes: true,
                                attributeFilter: ['style']
                            });
                            
                        } else {
                            console.error('AI助手初始化失败');
                        }
                    } catch (error) {
                        console.error('AI助手初始化出错:', error);
                    }
                };
                configScript.onerror = function() {
                    console.error('AI配置脚本加载失败');
                };
                document.head.appendChild(configScript);
            };
            aiScript.onerror = function() {
                console.error('AI助手脚本加载失败');
            };
            document.head.appendChild(aiScript);
        }
        
        // 页面加载完成后初始化AI助手
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(initAI, 500);
            });
        } else {
            setTimeout(initAI, 500);
        }
    </script>
</body>
</html>