/**
 * AI智能评价系统配置文件
 * 提供学习数据分析和个性化评价功能
 */

// AI评价系统配置
const AI_EVALUATION_CONFIG = {
    // 使用与主系统相同的API配置
    apiKey: '31c24cd84f6141e2baf6ea33df124055.BBvmeBEzCdBmzdTf',
    apiUrl: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'GLM-4.5',
    
    // 评价维度配置
    evaluationDimensions: {
        hazardIdentification: {
            name: '数据隐患识别',
            weight: 0.25,
            description: '识别和防范各种数据安全隐患的能力'
        },
        safeSharing: {
            name: '安全分享意识',
            weight: 0.25,
            description: '在社交媒体等平台安全分享信息的意识'
        },
        dataBackup: {
            name: '数据备份技能',
            weight: 0.25,
            description: '理解数据备份重要性并掌握基本操作技能'
        },
        protectionMeasures: {
            name: '保护措施应用',
            weight: 0.25,
            description: '针对不同场景选择和应用合适保护措施的能力'
        }
    },
    
    // 评价问题模板
    questionTemplates: [
        {
            id: 'reflection',
            question: "在这次数据安全大冒险中，哪个环节让你觉得最有趣或者最有收获？跟我分享一下你的感受吧！",
            type: 'open',
            evaluates: ['overall_engagement', 'reflection_ability']
        },
        {
            id: 'hazard_scenario',
            question: "想象一下，你正在玩手机，突然弹出一个广告说'恭喜你！点击这里免费获得最新游戏皮肤！'你会怎么做呢？说说你的想法？",
            type: 'scenario',
            evaluates: ['hazardIdentification', 'critical_thinking']
        },
        {
            id: 'sharing_practice',
            question: "假如你要在朋友圈晒一张在学校门口和同学们的合影，你会怎么处理这张照片？有什么需要注意的地方吗？",
            type: 'application',
            evaluates: ['safeSharing', 'privacy_awareness']
        },
        {
            id: 'backup_understanding',
            question: "哎呀！如果有一天你的电脑突然坏掉了，开不了机了，你觉得哪些东西丢了会让你最心疼？为什么我们要做备份呢？",
            type: 'understanding',
            evaluates: ['dataBackup', 'value_recognition']
        },
        {
            id: 'protection_application',
            question: "你在图书馆用公共WiFi写作业，需要把作业发给老师。这时候你会怎么保护自己的信息安全呢？",
            type: 'scenario',
            evaluates: ['protectionMeasures', 'situational_awareness']
        }
    ],
    
    // 学习行为分析指标
    behaviorMetrics: {
        engagement: {
            name: '学习投入度',
            indicators: ['total_time', 'completion_rate', 'interaction_frequency']
        },
        exploration: {
            name: '主动探索性',
            indicators: ['ai_questions', 'help_seeking', 'retry_attempts']
        },
        comprehension: {
            name: '理解深度',
            indicators: ['response_quality', 'concept_application', 'example_relevance']
        },
        retention: {
            name: '知识保持',
            indicators: ['consistency', 'concept_recall', 'transfer_ability']
        }
    }
};

/**
 * 学习数据收集器
 */
class LearningDataCollector {
    constructor() {
        this.sessionData = {
            startTime: Date.now(),
            interactions: [],
            completedLevels: [],
            aiQuestions: 0,
            totalTime: 0,
            accuracyScores: {},
            behaviorPatterns: []
        };
        
        this.initializeTracking();
    }
    
    // 初始化数据追踪
    initializeTracking() {
        // 追踪页面访问时间
        this.trackPageTime();
        
        // 追踪AI互动
        this.trackAIInteractions();
        
        // 追踪学习完成情况
        this.trackLearningProgress();
    }
    
    // 追踪页面停留时间
    trackPageTime() {
        let startTime = Date.now();
        
        window.addEventListener('beforeunload', () => {
            const timeSpent = Date.now() - startTime;
            this.sessionData.totalTime += timeSpent;
            this.saveSessionData();
        });
        
        // 定期保存数据
        setInterval(() => {
            this.saveSessionData();
        }, 30000); // 每30秒保存一次
    }
    
    // 追踪AI助手互动
    trackAIInteractions() {
        // 监听AI助手的使用情况
        if (window.aiAssistant) {
            const originalAddMessage = window.aiAssistant.addMessage;
            window.aiAssistant.addMessage = (content, type) => {
                if (type === 'user') {
                    this.sessionData.aiQuestions++;
                    this.sessionData.interactions.push({
                        type: 'ai_question',
                        content: content,
                        timestamp: Date.now()
                    });
                }
                return originalAddMessage.call(window.aiAssistant, content, type);
            };
        }
    }
    
    // 追踪学习进度
    trackLearningProgress() {
        // 监听关卡完成情况
        const originalSetItem = localStorage.setItem;
        localStorage.setItem = (key, value) => {
            if (key.includes('level') && key.includes('completed') && value === 'true') {
                const level = key.match(/level(\d+)/)?.[1];
                if (level && !this.sessionData.completedLevels.includes(level)) {
                    this.sessionData.completedLevels.push(level);
                    this.sessionData.interactions.push({
                        type: 'level_completed',
                        level: level,
                        timestamp: Date.now()
                    });
                }
            }
            return originalSetItem.call(localStorage, key, value);
        };
    }
    
    // 保存会话数据
    saveSessionData() {
        localStorage.setItem('learning_session_data', JSON.stringify(this.sessionData));
    }
    
    // 获取学习数据摘要
    getDataSummary() {
        const currentTime = Date.now();
        const totalTime = Math.floor((currentTime - this.sessionData.startTime) / 60000); // 分钟
        
        return {
            totalTime: totalTime,
            completedLevels: this.sessionData.completedLevels.length,
            aiInteractions: this.sessionData.aiQuestions,
            engagementLevel: this.calculateEngagementLevel(),
            learningPattern: this.analyzeLearningPattern()
        };
    }
    
    // 计算投入度水平
    calculateEngagementLevel() {
        const factors = {
            timeSpent: Math.min(this.sessionData.totalTime / (30 * 60 * 1000), 1), // 30分钟为满分
            interactions: Math.min(this.sessionData.aiQuestions / 10, 1), // 10次互动为满分
            completion: this.sessionData.completedLevels.length / 4 // 4个关卡
        };
        
        const average = (factors.timeSpent + factors.interactions + factors.completion) / 3;
        
        if (average >= 0.8) return 'high';
        if (average >= 0.5) return 'medium';
        return 'low';
    }
    
    // 分析学习模式
    analyzeLearningPattern() {
        const interactions = this.sessionData.interactions;
        const aiQuestions = interactions.filter(i => i.type === 'ai_question').length;
        const completions = interactions.filter(i => i.type === 'level_completed').length;
        
        if (aiQuestions > 8) return 'interactive';
        if (completions === 4 && aiQuestions < 3) return 'independent';
        return 'balanced';
    }
}

/**
 * AI评价引擎
 */
class AIEvaluationEngine {
    constructor(config = AI_EVALUATION_CONFIG) {
        this.config = config;
        this.dataCollector = new LearningDataCollector();
    }
    
    // 生成评价问题
    async generateEvaluationQuestions(learningData) {
        const questions = [...this.config.questionTemplates];
        
        // 根据学习数据个性化问题
        if (learningData.aiInteractions > 10) {
            questions.push({
                id: 'ai_interaction',
                question: "你在学习过程中经常向AI助手提问，这说明你很善于主动学习。能分享一个AI助手帮助你解决的具体问题吗？",
                type: 'reflection',
                evaluates: ['learning_strategy', 'help_seeking']
            });
        }
        
        if (learningData.completedLevels === 4) {
            questions.push({
                id: 'completion_reflection',
                question: "恭喜你完成了所有关卡！现在你觉得自己在数据安全方面最大的收获是什么？",
                type: 'reflection',
                evaluates: ['overall_achievement', 'self_assessment']
            });
        }
        
        return questions.slice(0, 5); // 限制问题数量
    }
    
    // 分析用户回答
    async analyzeResponse(question, answer) {
        if (!this.config.apiKey) {
            return this.generateMockAnalysis(question, answer);
        }
        
        const prompt = this.buildAnalysisPrompt(question, answer);
        
        try {
            const response = await fetch(this.config.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiKey}`
                },
                body: JSON.stringify({
                    model: this.config.model,
                    messages: [
                        { role: 'system', content: prompt.system },
                        { role: 'user', content: prompt.user }
                    ],
                    temperature: 0.7,
                    max_tokens: 500
                })
            });
            
            const data = await response.json();
            return this.parseAIAnalysis(data.choices[0].message.content);
        } catch (error) {
            console.error('AI分析失败:', error);
            return this.generateMockAnalysis(question, answer);
        }
    }
    
    // 构建分析提示词
    buildAnalysisPrompt(question, answer) {
        return {
            system: `你是一位专业的小学教育评价专家，专门评估四年级学生的数据安全学习情况。
            
请根据学生的回答，从以下维度进行评价：
1. 理解程度 (1-5分)
2. 应用能力 (1-5分)  
3. 安全意识 (1-5分)
4. 表达清晰度 (1-5分)

请给出简短的评价理由，并提供1-2个具体的改进建议。

回答格式：
{
  "understanding": 分数,
  "application": 分数,
  "awareness": 分数,
  "expression": 分数,
  "feedback": "评价理由",
  "suggestions": ["建议1", "建议2"]
}`,
            user: `问题：${question.question}\n学生回答：${answer}`
        };
    }
    
    // 解析AI分析结果
    parseAIAnalysis(aiResponse) {
        try {
            return JSON.parse(aiResponse);
        } catch (error) {
            // 如果解析失败，返回默认分析
            return {
                understanding: 4,
                application: 4,
                awareness: 4,
                expression: 4,
                feedback: "回答体现了对数据安全的基本理解",
                suggestions: ["可以结合更多生活实例", "继续保持学习积极性"]
            };
        }
    }
    
    // 生成模拟分析（当API不可用时）
    generateMockAnalysis(question, answer) {
        const length = answer.length;
        const hasExamples = answer.includes('比如') || answer.includes('例如') || answer.includes('像');
        const hasReasoning = answer.includes('因为') || answer.includes('所以') || answer.includes('这样');
        
        let understanding = 3;
        let application = 3;
        let awareness = 3;
        let expression = 3;
        
        // 根据回答长度和内容调整分数
        if (length > 50) expression += 1;
        if (hasExamples) application += 1;
        if (hasReasoning) understanding += 1;
        if (answer.includes('安全') || answer.includes('保护') || answer.includes('隐私')) awareness += 1;
        
        return {
            understanding: Math.min(understanding, 5),
            application: Math.min(application, 5),
            awareness: Math.min(awareness, 5),
            expression: Math.min(expression, 5),
            feedback: "回答展现了良好的学习态度和基本的安全意识",
            suggestions: [
                "可以尝试用更多具体例子来说明观点",
                "继续在生活中实践所学的安全知识"
            ]
        };
    }
    
    // 生成综合评价报告
    generateComprehensiveReport(responses, learningData) {
        const scores = this.calculateDimensionScores(responses);
        const behaviorAnalysis = this.analyzeLearningBehavior(learningData);
        const achievements = this.identifyAchievements(scores, behaviorAnalysis);
        const recommendations = this.generateRecommendations(scores, behaviorAnalysis);
        
        return {
            overallScore: this.calculateOverallScore(scores),
            dimensionScores: scores,
            behaviorAnalysis: behaviorAnalysis,
            achievements: achievements,
            recommendations: recommendations,
            learningPath: this.suggestLearningPath(scores)
        };
    }
    
    // 计算各维度分数
    calculateDimensionScores(responses) {
        const dimensions = Object.keys(this.config.evaluationDimensions);
        const scores = {};
        
        dimensions.forEach(dim => {
            const relevantResponses = responses.filter(r => 
                r.question.evaluates && r.question.evaluates.includes(dim)
            );
            
            if (relevantResponses.length > 0) {
                const avgScore = relevantResponses.reduce((sum, r) => {
                    return sum + (r.analysis.understanding + r.analysis.application + r.analysis.awareness) / 3;
                }, 0) / relevantResponses.length;
                
                scores[dim] = Math.round(avgScore * 20); // 转换为百分制
            } else {
                scores[dim] = 80; // 默认分数
            }
        });
        
        return scores;
    }
    
    // 分析学习行为
    analyzeLearningBehavior(learningData) {
        return {
            engagementLevel: learningData.engagementLevel,
            learningStyle: learningData.learningPattern,
            interactionFrequency: learningData.aiInteractions > 8 ? 'high' : 
                                 learningData.aiInteractions > 3 ? 'medium' : 'low',
            timeManagement: learningData.totalTime > 25 ? 'thorough' : 
                           learningData.totalTime > 15 ? 'adequate' : 'efficient',
            completionRate: (learningData.completedLevels / 4) * 100
        };
    }
    
    // 识别学习成就
    identifyAchievements(scores, behavior) {
        const achievements = [];
        
        if (behavior.completionRate === 100) {
            achievements.push({
                title: '数据安全小卫士',
                description: '完成所有学习关卡',
                icon: '🛡️',
                level: 'gold'
            });
        }
        
        if (scores.hazardIdentification >= 85) {
            achievements.push({
                title: '隐患识别专家',
                description: '在数据隐患识别方面表现优秀',
                icon: '🔍',
                level: 'silver'
            });
        }
        
        if (scores.safeSharing >= 85) {
            achievements.push({
                title: '安全分享达人',
                description: '具备优秀的安全分享意识',
                icon: '📱',
                level: 'silver'
            });
        }
        
        if (behavior.interactionFrequency === 'high') {
            achievements.push({
                title: '积极学习者',
                description: '积极主动地寻求帮助和指导',
                icon: '🙋',
                level: 'bronze'
            });
        }
        
        return achievements;
    }
    
    // 生成改进建议
    generateRecommendations(scores, behavior) {
        const recommendations = [];
        
        // 基于分数的建议
        Object.keys(scores).forEach(dimension => {
            if (scores[dimension] < 80) {
                const dimConfig = this.config.evaluationDimensions[dimension];
                recommendations.push({
                    type: 'improvement',
                    dimension: dimConfig.name,
                    suggestion: `建议加强${dimConfig.name}方面的练习和理解`
                });
            }
        });
        
        // 基于行为的建议
        if (behavior.interactionFrequency === 'low') {
            recommendations.push({
                type: 'behavior',
                suggestion: '建议在学习过程中多向AI助手提问，加强互动学习'
            });
        }
        
        if (behavior.engagementLevel === 'low') {
            recommendations.push({
                type: 'engagement',
                suggestion: '建议分配更多时间深入理解学习内容'
            });
        }
        
        return recommendations;
    }
    
    // 计算总分
    calculateOverallScore(scores) {
        const weights = this.config.evaluationDimensions;
        let totalScore = 0;
        let totalWeight = 0;
        
        Object.keys(scores).forEach(dim => {
            if (weights[dim]) {
                totalScore += scores[dim] * weights[dim].weight;
                totalWeight += weights[dim].weight;
            }
        });
        
        return Math.round(totalScore / totalWeight);
    }
    
    // 建议学习路径
    suggestLearningPath(scores) {
        const weakAreas = Object.keys(scores).filter(dim => scores[dim] < 80);
        
        if (weakAreas.length === 0) {
            return {
                type: 'advanced',
                suggestion: '建议学习更高级的网络安全知识',
                resources: ['网络安全进阶课程', '密码学基础', '隐私保护技术']
            };
        }
        
        return {
            type: 'reinforcement',
            suggestion: '建议重点复习薄弱环节',
            focus: weakAreas.map(dim => this.config.evaluationDimensions[dim].name),
            resources: ['相关练习题', '案例分析', '实践活动']
        };
    }
}

// 导出配置和类
if (typeof window !== 'undefined') {
    window.AI_EVALUATION_CONFIG = AI_EVALUATION_CONFIG;
    window.LearningDataCollector = LearningDataCollector;
    window.AIEvaluationEngine = AIEvaluationEngine;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AI_EVALUATION_CONFIG,
        LearningDataCollector,
        AIEvaluationEngine
    };
}
